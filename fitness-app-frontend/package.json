{"name": "fitness-app-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.1", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-oauth2-code-pkce": "^1.23.0", "react-redux": "^9.2.0", "react-router": "^7.6.2", "react-router-dom": "^7.6.2", "redux": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}
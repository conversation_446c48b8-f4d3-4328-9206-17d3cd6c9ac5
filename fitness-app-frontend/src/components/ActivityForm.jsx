import { Box, Button, FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import React, { useState } from 'react'
import { addActivity } from '../services/api'


const ActivityForm = ({ onActivityAdded }) => {

    const [activity, setActivity] = useState({
        type: "RUNNING", duration: '', caloriesBurned: '',
        additionalMetrics: {}
    });

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validate form data
        if (!activity.duration || !activity.caloriesBurned) {
            alert('Please fill in all required fields (Duration and Calories Burned)');
            return;
        }

        // Convert string values to numbers
        const activityData = {
            ...activity,
            duration: parseInt(activity.duration),
            caloriesBurned: parseInt(activity.caloriesBurned)
        };

        // Log the data being sent
        console.log('Sending activity data:', activityData);

        try {
            const response = await addActivity(activityData);
            console.log('Activity added successfully:', response);
            onActivityAdded();
            setActivity({ type: "RUNNING", duration: '', caloriesBurned: '', additionalMetrics: {}});
        } catch (error) {
            console.error('Full error object:', error);
            console.error('Error response:', error.response?.data);
            console.error('Error status:', error.response?.status);
            console.error('Error headers:', error.response?.headers);

            // Show user-friendly error message
            alert(`Failed to add activity: ${error.response?.data?.message || error.message}`);
        }
    }
    
  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mb: 4 }}>
    <FormControl fullWidth sx={{mb: 2}}>
        <InputLabel>Activity Type</InputLabel>
        <Select
            value={activity.type}
            onChange={(e) => setActivity({...activity, type: e.target.value})}>
                <MenuItem value="RUNNING">Running</MenuItem>
                <MenuItem value="WALKING">Walking</MenuItem>
                <MenuItem value="CYCLING">Cycling</MenuItem>
            </Select>
    </FormControl>
    <TextField fullWidth
                label="Duration (Minutes)"
                type='number'
                sx={{ mb: 2}}
                value={activity.duration}
                onChange={(e) => setActivity({...activity, duration: e.target.value})}/>

<TextField fullWidth
                label="Calories Burned"
                type='number'
                sx={{ mb: 2}}
                value={activity.caloriesBurned}
                onChange={(e) => setActivity({...activity, caloriesBurned: e.target.value})}/>

<Button type='submit' variant='contained'>
    Add Activity
</Button>
  </Box>
  )
}

export default ActivityForm
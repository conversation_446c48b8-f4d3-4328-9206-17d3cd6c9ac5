spring:
  application:
    name: api-gateway
  config:
    import: optional:configserver:http://localhost:8888
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8080/realms/fitness
          jwk-set-uri: http://localhost:8080/realms/fitness/protocol/openid-connect/certs
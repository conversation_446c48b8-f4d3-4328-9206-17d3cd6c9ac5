<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="aiservice" />
        <module name="configserver" />
        <module name="eureka" />
        <module name="gateway" />
      </profile>
      <profile name="Annotation profile for userservice" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar" />
        </processorPath>
        <module name="activityservice" />
        <module name="userservice" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="activityservice" options="-parameters" />
      <module name="aiservice" options="-parameters" />
      <module name="configserver" options="-parameters" />
      <module name="eureka" options="-parameters" />
      <module name="gateway" options="-parameters" />
      <module name="userservice" options="-parameters" />
    </option>
  </component>
</project>
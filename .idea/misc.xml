<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MaterialThemeProjectNewConfig">
    <option name="metadata">
      <MTProjectMetadataState>
        <option name="migrated" value="true" />
        <option name="pristineConfig" value="false" />
        <option name="userId" value="-434dff4c:19593257a9f:-7ffe" />
      </MTProjectMetadataState>
    </option>
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/userservice/pom.xml" />
        <option value="$PROJECT_DIR$/activityservice/pom.xml" />
        <option value="$PROJECT_DIR$/eureka/pom.xml" />
        <option value="$PROJECT_DIR$/aiservice/pom.xml" />
        <option value="$PROJECT_DIR$/configserver/pom.xml" />
        <option value="$PROJECT_DIR$/gateway/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>
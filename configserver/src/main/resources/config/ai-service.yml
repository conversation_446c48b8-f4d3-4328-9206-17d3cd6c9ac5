spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/fitnessrecommendation
      database: fitnessrecommendation
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/

server:
  port: 8083

rabbitmq:
  exchange:
    name: fitness.exchange
  queue:
    name: activity.queue
  routing:
    key: activity.tracking

gemini:
  api:
    url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=
    key: AIzaSyBPjyYshK5Lneg24XVcr4Uj5pLAuAkpF9k
